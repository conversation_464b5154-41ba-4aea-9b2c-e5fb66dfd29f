/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/browser-automation/execute/route";
exports.ids = ["app/api/browser-automation/execute/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/browser-automation/execute/route.ts */ \"(rsc)/./src/app/api/browser-automation/execute/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/browser-automation/execute/route\",\n        pathname: \"/api/browser-automation/execute\",\n        filename: \"route\",\n        bundlePath: \"app/api/browser-automation/execute/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\browser-automation\\\\execute\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_browser_automation_execute_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIvaW5kZXguanM/bmFtZT1hcHAlMkZhcGklMkZicm93c2VyLWF1dG9tYXRpb24lMkZleGVjdXRlJTJGcm91dGUmcGFnZT0lMkZhcGklMkZicm93c2VyLWF1dG9tYXRpb24lMkZleGVjdXRlJTJGcm91dGUmYXBwUGF0aHM9JnBhZ2VQYXRoPXByaXZhdGUtbmV4dC1hcHAtZGlyJTJGYXBpJTJGYnJvd3Nlci1hdXRvbWF0aW9uJTJGZXhlY3V0ZSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDUm9LZXklMjBBcHAlNUNyb2tleS1hcHAlNUNzcmMlNUNhcHAmcGFnZUV4dGVuc2lvbnM9dHN4JnBhZ2VFeHRlbnNpb25zPXRzJnBhZ2VFeHRlbnNpb25zPWpzeCZwYWdlRXh0ZW5zaW9ucz1qcyZyb290RGlyPUMlM0ElNUNSb0tleSUyMEFwcCU1Q3Jva2V5LWFwcCZpc0Rldj10cnVlJnRzY29uZmlnUGF0aD10c2NvbmZpZy5qc29uJmJhc2VQYXRoPSZhc3NldFByZWZpeD0mbmV4dENvbmZpZ091dHB1dD0mcHJlZmVycmVkUmVnaW9uPSZtaWRkbGV3YXJlQ29uZmlnPWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBK0Y7QUFDdkM7QUFDcUI7QUFDOEI7QUFDM0c7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHlHQUFtQjtBQUMzQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLFlBQVk7QUFDWixDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsUUFBUSxzREFBc0Q7QUFDOUQ7QUFDQSxXQUFXLDRFQUFXO0FBQ3RCO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDMEY7O0FBRTFGIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgQXBwUm91dGVSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvYXBwLXJvdXRlL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXFJvS2V5IEFwcFxcXFxyb2tleS1hcHBcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcYnJvd3Nlci1hdXRvbWF0aW9uXFxcXGV4ZWN1dGVcXFxccm91dGUudHNcIjtcbi8vIFdlIGluamVjdCB0aGUgbmV4dENvbmZpZ091dHB1dCBoZXJlIHNvIHRoYXQgd2UgY2FuIHVzZSB0aGVtIGluIHRoZSByb3V0ZVxuLy8gbW9kdWxlLlxuY29uc3QgbmV4dENvbmZpZ091dHB1dCA9IFwiXCJcbmNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IEFwcFJvdXRlUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLkFQUF9ST1VURSxcbiAgICAgICAgcGFnZTogXCIvYXBpL2Jyb3dzZXItYXV0b21hdGlvbi9leGVjdXRlL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvYnJvd3Nlci1hdXRvbWF0aW9uL2V4ZWN1dGVcIixcbiAgICAgICAgZmlsZW5hbWU6IFwicm91dGVcIixcbiAgICAgICAgYnVuZGxlUGF0aDogXCJhcHAvYXBpL2Jyb3dzZXItYXV0b21hdGlvbi9leGVjdXRlL3JvdXRlXCJcbiAgICB9LFxuICAgIHJlc29sdmVkUGFnZVBhdGg6IFwiQzpcXFxcUm9LZXkgQXBwXFxcXHJva2V5LWFwcFxcXFxzcmNcXFxcYXBwXFxcXGFwaVxcXFxicm93c2VyLWF1dG9tYXRpb25cXFxcZXhlY3V0ZVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHdvcmtBc3luY1N0b3JhZ2UsIHdvcmtVbml0QXN5bmNTdG9yYWdlLCBzZXJ2ZXJIb29rcyB9ID0gcm91dGVNb2R1bGU7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHdvcmtBc3luY1N0b3JhZ2UsXG4gICAgICAgIHdvcmtVbml0QXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgd29ya0FzeW5jU3RvcmFnZSwgd29ya1VuaXRBc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzLCBwYXRjaEZldGNoLCAgfTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9YXBwLXJvdXRlLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/browser-automation/execute/route.ts":
/*!*********************************************************!*\
  !*** ./src/app/api/browser-automation/execute/route.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\nconst supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY);\n// Browser automation service URL - environment aware\nconst getBrowserAutomationServiceUrl = ()=>{\n    // Check if explicitly set in environment\n    if (process.env.BROWSER_AUTOMATION_SERVICE_URL) {\n        return process.env.BROWSER_AUTOMATION_SERVICE_URL;\n    }\n    // Auto-detect based on environment\n    if (false) {}\n    // Development default\n    return 'http://localhost:8000';\n};\nconst BROWSER_AUTOMATION_SERVICE_URL = getBrowserAutomationServiceUrl();\nasync function POST(request) {\n    try {\n        const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n        const authCookie = cookieStore.get('sb-access-token');\n        if (!authCookie) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get user from auth cookie\n        const { data: { user }, error: authError } = await supabase.auth.getUser(authCookie.value);\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const body = await request.json();\n        const { task, task_type = 'navigation', config_id, extracted_parameters = {}, user_tier, stream = false } = body;\n        if (!task || !config_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Task and config_id are required'\n            }, {\n                status: 400\n            });\n        }\n        // Verify the config belongs to the user and has browser automation enabled\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, user_id, browser_automation_enabled, name').eq('id', config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Configuration not found'\n            }, {\n                status: 404\n            });\n        }\n        if (!config.browser_automation_enabled) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation is not enabled for this configuration'\n            }, {\n                status: 403\n            });\n        }\n        // Get user tier and validate access\n        const { data: subscription, error: subError } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const userTier = subscription?.tier || 'free';\n        // Allow starter, professional, and enterprise tiers\n        const allowedTiers = [\n            'starter',\n            'professional',\n            'enterprise'\n        ];\n        if (!allowedTiers.includes(userTier)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Browser automation requires Starter plan or higher'\n            }, {\n                status: 403\n            });\n        }\n        // Check and enforce quota limits\n        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format\n        // Get or create usage record for current month\n        let { data: usage, error: usageError } = await supabase.from('browser_automation_usage').select('*').eq('user_id', user.id).eq('month_year', currentMonth).single();\n        if (usageError && usageError.code !== 'PGRST116') {\n            console.error('Error checking browser automation usage:', usageError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to check usage quota'\n            }, {\n                status: 500\n            });\n        }\n        // Define tier limits according to Milestone 2 specification\n        const tierLimits = {\n            free: 0,\n            starter: 15,\n            professional: -1,\n            enterprise: -1 // Custom workflows + parallel browsing (unlimited)\n        };\n        const monthlyLimit = tierLimits[userTier] || 0;\n        if (!usage) {\n            // Create new usage record\n            const { data: newUsage, error: createError } = await supabase.from('browser_automation_usage').insert({\n                user_id: user.id,\n                month_year: currentMonth,\n                tier: userTier,\n                tasks_used: 0,\n                tasks_limit: monthlyLimit\n            }).select().single();\n            if (createError) {\n                console.error('Error creating usage record:', createError);\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Failed to initialize usage tracking'\n                }, {\n                    status: 500\n                });\n            }\n            usage = newUsage;\n        }\n        // Check if user has exceeded quota (skip check for unlimited tiers)\n        if (usage.tasks_limit !== -1 && usage.tasks_used >= usage.tasks_limit) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: `Monthly browser automation quota exceeded. You have used ${usage.tasks_used}/${usage.tasks_limit} tasks this month.`,\n                quota_exceeded: true,\n                tasks_used: usage.tasks_used,\n                tasks_limit: usage.tasks_limit,\n                upgrade_required: userTier === 'starter' ? 'professional' : null\n            }, {\n                status: 429\n            });\n        }\n        // Get user's API keys and roles for this config\n        const { data: apiKeys, error: keysError } = await supabase.from('api_keys').select(`\n        id,\n        provider,\n        predefined_model_id,\n        temperature,\n        label,\n        api_key_encrypted,\n        key_role_assignments (\n          role_name\n        )\n      `).eq('custom_api_config_id', config_id).eq('status', 'active');\n        if (keysError || !apiKeys || apiKeys.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'No active API keys found for this configuration'\n            }, {\n                status: 400\n            });\n        }\n        // Prepare the request for the browser automation service\n        // This integrates with RouKey's BYOK system - user's API keys and role assignments\n        const browserAutomationRequest = {\n            task,\n            task_type,\n            user_id: user.id,\n            config_id,\n            config_name: config.name,\n            user_tier: userTier,\n            extracted_parameters,\n            // Pass user's BYOK API keys with role assignments for intelligent routing\n            api_keys: apiKeys.map((key)=>({\n                    id: key.id,\n                    provider: key.provider,\n                    model: key.predefined_model_id,\n                    api_key: key.api_key,\n                    temperature: key.temperature,\n                    label: key.label,\n                    roles: key.key_role_assignments?.map((assignment)=>assignment.role_name) || [],\n                    // Browser automation will use these keys for LLM calls via role routing\n                    routing_strategy: config.routing_strategy\n                })),\n            // Browser configuration options (user configurable)\n            browser_headless: extracted_parameters.headless ?? true,\n            browser_viewport_width: extracted_parameters.viewport_width ?? 1920,\n            browser_viewport_height: extracted_parameters.viewport_height ?? 1080,\n            browser_slow_mo: extracted_parameters.slow_mo ?? 0,\n            browser_devtools: extracted_parameters.devtools ?? false,\n            stream\n        };\n        // Call the browser automation service\n        const endpoint = stream ? '/api/v1/browser/execute/stream' : '/api/v1/browser/execute';\n        const response = await fetch(`${BROWSER_AUTOMATION_SERVICE_URL}${endpoint}`, {\n            method: 'POST',\n            headers: {\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify(browserAutomationRequest)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({\n                    error: 'Browser automation service error'\n                }));\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: errorData.error || 'Browser automation service failed'\n            }, {\n                status: response.status\n            });\n        }\n        // If streaming, return the stream\n        if (stream) {\n            return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(response.body, {\n                headers: {\n                    'Content-Type': 'text/event-stream',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }\n            });\n        }\n        // For non-streaming, return the JSON response\n        const result = await response.json();\n        // Consume quota after successful execution (only for limited tiers)\n        if (result.success !== false) {\n            try {\n                // Only increment usage for limited tiers (not unlimited)\n                if (usage.tasks_limit !== -1) {\n                    await supabase.from('browser_automation_usage').update({\n                        tasks_used: usage.tasks_used + 1,\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                } else {\n                    // For unlimited tiers, just update last_task_at\n                    await supabase.from('browser_automation_usage').update({\n                        last_task_at: new Date().toISOString(),\n                        updated_at: new Date().toISOString()\n                    }).eq('user_id', user.id).eq('month_year', currentMonth);\n                }\n                // Log analytics\n                await supabase.from('browser_automation_analytics').insert({\n                    user_id: user.id,\n                    task_id: result.task_id,\n                    event_type: 'task_completed',\n                    event_data: {\n                        task_type,\n                        extracted_parameters,\n                        execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null,\n                        steps_completed: result.execution_metadata?.steps_completed\n                    },\n                    tier: userTier,\n                    config_id,\n                    success: result.success,\n                    execution_time_ms: result.execution_metadata?.execution_time_seconds ? result.execution_metadata.execution_time_seconds * 1000 : null\n                });\n                const displayUsage = usage.tasks_limit === -1 ? 'unlimited' : `${usage.tasks_used + (usage.tasks_limit !== -1 ? 1 : 0)}/${usage.tasks_limit}`;\n                console.log(`[Browser Automation] Task completed for user ${user.id}. Usage: ${displayUsage}`);\n            } catch (quotaError) {\n                console.error('Error updating quota:', quotaError);\n            // Don't fail the request if quota update fails\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('Error executing browser automation:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/browser-automation/execute/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&page=%2Fapi%2Fbrowser-automation%2Fexecute%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fbrowser-automation%2Fexecute%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();