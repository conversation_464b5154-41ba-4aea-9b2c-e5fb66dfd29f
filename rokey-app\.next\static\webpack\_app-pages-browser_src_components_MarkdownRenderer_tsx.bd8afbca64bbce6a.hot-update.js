"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_MarkdownRenderer_tsx",{

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\n// Dynamic imports to isolate problematic packages with error handling\nconst ReactMarkdown = dynamic(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-markdown_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/index.js\")).catch(()=>{\n        // Fallback if react-markdown fails to load\n        return {\n            default: (param)=>{\n                let { children } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"whitespace-pre-wrap\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 63\n                }, undefined);\n            }\n        };\n    }), {\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, undefined)\n});\n_c = ReactMarkdown;\n// Dynamic import for syntax highlighter with fallback\nconst SyntaxHighlighter = dynamic(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/index.js\")).then((mod)=>mod.Prism), {\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                children: \"Loading syntax highlighter...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n});\n_c1 = SyntaxHighlighter;\nconst syntaxHighlighterStyle = dynamic(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_styles_prism_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\")).then((mod)=>mod.oneDark), {\n    ssr: false\n});\n// Enhanced syntax highlighter with proper colors\nconst EnhancedSyntaxHighlighter = (param)=>{\n    let { children, language } = param;\n    _s();\n    const [style, setStyle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n            // Load the style dynamically\n            __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_styles_prism_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\")).then({\n                \"EnhancedSyntaxHighlighter.useEffect\": (mod)=>{\n                    setStyle(mod.oneDark);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]).catch({\n                \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n                    // Fallback to null if style loading fails\n                    setStyle(null);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]);\n        }\n    }[\"EnhancedSyntaxHighlighter.useEffect\"], []);\n    if (style) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SyntaxHighlighter, {\n            style: style,\n            language: language || 'text',\n            PreTag: \"div\",\n            className: \"text-sm\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 88,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback while loading or if syntax highlighter fails\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSyntaxHighlighter, \"HRaQW7wquc8eCzy6OE2MQGOkRVI=\");\n_c2 = EnhancedSyntaxHighlighter;\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    _s1();\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"markdown-content \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 123,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 122,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 133,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                    remarkPlugins: [],\n                    components: {\n                        // Headers\n                        h1: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h2: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h3: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h4: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Paragraphs\n                        p: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Bold and italic\n                        strong: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        em: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Lists\n                        ul: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        ol: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        li: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Code blocks and inline code\n                        code: (param)=>{\n                            let { node, inline, className, children, ...props } = param;\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with simple syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EnhancedSyntaxHighlighter, {\n                                                language: language,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 243,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Links\n                        a: (param)=>{\n                            let { children, href } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Tables\n                        table: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        thead: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 288,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tbody: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tr: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        th: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        td: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 132,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 131,\n        columnNumber: 5\n    }, this);\n}\n_s1(MarkdownRenderer, \"Y10BwSn8jQ4wQtRAtRxoscEk2KA=\");\n_c3 = MarkdownRenderer;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ReactMarkdown\");\n$RefreshReg$(_c1, \"SyntaxHighlighter\");\n$RefreshReg$(_c2, \"EnhancedSyntaxHighlighter\");\n$RefreshReg$(_c3, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ })

});