"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code') {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\nfunction MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: {\n                        // Headers\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, void 0),\n                        h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, void 0),\n                        // Paragraphs\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, void 0),\n                        // Bold and italic\n                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, void 0),\n                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, void 0),\n                        // Lists\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, void 0),\n                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, void 0),\n                        // Code blocks and inline code\n                        code: ({ node, inline, className, children, ...props })=>{\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Check if this is a short single-line code snippet that should be treated as enhanced inline\n                                const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\\n') && !language;\n                                if (isShortSnippet) {\n                                    // Treat short snippets as enhanced inline code with subtle highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200\",\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                                // Handle actual code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Multi-line code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, void 0),\n                        // Links\n                        a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 11\n                            }, void 0),\n                        // Tables\n                        table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, void 0),\n                        thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, void 0),\n                        tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, void 0),\n                        tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, void 0),\n                        th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, void 0),\n                        td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, void 0),\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;