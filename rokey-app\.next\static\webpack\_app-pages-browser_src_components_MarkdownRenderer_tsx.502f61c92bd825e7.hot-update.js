"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_MarkdownRenderer_tsx",{

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    _s();\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"markdown-content \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 55,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 54,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_3__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_4__[\"default\"]\n                    ],\n                    components: {\n                        // Headers\n                        h1: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h2: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h3: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h4: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Paragraphs - handle code blocks properly\n                        p: (param)=>{\n                            let { children, node } = param;\n                            // Check if this paragraph contains a code element (which might be a code block)\n                            const childrenArray = Array.isArray(children) ? children : [\n                                children\n                            ];\n                            const hasBlockLevelContent = childrenArray.some((child)=>{\n                                // Check if child is a React element with a div (code block)\n                                return (child === null || child === void 0 ? void 0 : child.type) === 'div' || (child === null || child === void 0 ? void 0 : child.props) && child.props.className && child.props.className.includes('my-3');\n                            });\n                            if (hasBlockLevelContent) {\n                                // If paragraph contains block-level content, render as fragment to avoid nesting issues\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: children\n                                }, void 0, false);\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Bold and italic\n                        strong: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        em: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Lists\n                        ul: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        ol: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        li: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Code blocks and inline code\n                        code: (param)=>{\n                            let { node, inline, className, children, ...props } = param;\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 162,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Links\n                        a: (param)=>{\n                            let { children, href } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Tables\n                        table: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        thead: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tbody: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tr: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        th: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        td: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, this);\n}\n_s(MarkdownRenderer, \"Y10BwSn8jQ4wQtRAtRxoscEk2KA=\");\n_c = MarkdownRenderer;\nvar _c;\n$RefreshReg$(_c, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ })

});