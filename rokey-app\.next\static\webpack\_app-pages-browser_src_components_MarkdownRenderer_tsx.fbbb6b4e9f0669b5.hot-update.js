"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_MarkdownRenderer_tsx",{

/***/ "(app-pages-browser)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CopyButton */ \"(app-pages-browser)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_2__.Component {\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n}\n// Dynamic imports to isolate problematic packages with error handling\nconst ReactMarkdown = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-markdown_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/index.js\")).catch(()=>{\n        // Fallback if react-markdown fails to load\n        return {\n            default: (param)=>{\n                let { children } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    className: \"whitespace-pre-wrap\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 63\n                }, undefined);\n            }\n        };\n    }), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-markdown\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse bg-gray-100 rounded p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-4 bg-gray-300 rounded w-1/2\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, undefined)\n});\n_c = ReactMarkdown;\n// Dynamic import for syntax highlighter with fallback\nconst SyntaxHighlighter = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/index.js\")).then((mod)=>mod.Prism), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                children: \"Loading syntax highlighter...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 58,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, undefined)\n});\n_c1 = SyntaxHighlighter;\nconst syntaxHighlighterStyle = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_styles_prism_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\")).then((mod)=>mod.oneDark), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\MarkdownRenderer.tsx -> \" + \"react-syntax-highlighter/dist/esm/styles/prism\"\n        ]\n    },\n    ssr: false\n});\n// Enhanced syntax highlighter with proper colors\nconst EnhancedSyntaxHighlighter = (param)=>{\n    let { children, language } = param;\n    _s();\n    const [style, setStyle] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n            // Load the style dynamically\n            __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_react-syntax-highlighter_dist_esm_styles_prism_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! react-syntax-highlighter/dist/esm/styles/prism */ \"(app-pages-browser)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/index.js\")).then({\n                \"EnhancedSyntaxHighlighter.useEffect\": (mod)=>{\n                    setStyle(mod.oneDark);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]).catch({\n                \"EnhancedSyntaxHighlighter.useEffect\": ()=>{\n                    // Fallback to null if style loading fails\n                    setStyle(null);\n                }\n            }[\"EnhancedSyntaxHighlighter.useEffect\"]);\n        }\n    }[\"EnhancedSyntaxHighlighter.useEffect\"], []);\n    if (style) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SyntaxHighlighter, {\n            style: style,\n            language: language || 'text',\n            PreTag: \"div\",\n            className: \"text-sm\",\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Fallback while loading or if syntax highlighter fails\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-900 text-gray-100 p-4 rounded-lg text-sm font-mono overflow-x-auto\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\n_s(EnhancedSyntaxHighlighter, \"HRaQW7wquc8eCzy6OE2MQGOkRVI=\");\n_c2 = EnhancedSyntaxHighlighter;\nfunction MarkdownRenderer(param) {\n    let { content, className = '' } = param;\n    _s1();\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"markdown-content \".concat(className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-gray-900 leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 120,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"markdown-content \".concat(className),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ReactMarkdown, {\n                    remarkPlugins: [],\n                    components: {\n                        // Headers\n                        h1: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h2: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h3: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        h4: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Paragraphs\n                        p: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-gray-900 break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Bold and italic\n                        strong: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        em: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Lists\n                        ul: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        ol: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        li: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Code blocks and inline code\n                        code: (param)=>{\n                            let { node, inline, className, children, ...props } = param;\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Handle code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with simple syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SimpleSyntaxHighlighter, {\n                                                language: language,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 231,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-100 text-gray-900 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-700\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Links\n                        a: (param)=>{\n                            let { children, href } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 266,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Tables\n                        table: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-200 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 279,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        thead: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tbody: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-200 bg-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        tr: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-50\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        th: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 300,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        td: (param)=>{\n                            let { children } = param;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-gray-900 border-b border-gray-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, void 0);\n                        },\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-200\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 136,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, this);\n}\n_s1(MarkdownRenderer, \"Y10BwSn8jQ4wQtRAtRxoscEk2KA=\");\n_c3 = MarkdownRenderer;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"ReactMarkdown\");\n$RefreshReg$(_c1, \"SyntaxHighlighter\");\n$RefreshReg$(_c2, \"EnhancedSyntaxHighlighter\");\n$RefreshReg$(_c3, \"MarkdownRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL01hcmtkb3duUmVuZGVyZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1DO0FBQ3lDO0FBQ3RDO0FBRXRDLGtFQUFrRTtBQUNsRSxNQUFNTSxzQkFBc0JGLDRDQUFTQTtJQVNuQyxPQUFPRywyQkFBMkI7UUFDaEMsT0FBTztZQUFFQyxVQUFVO1FBQUs7SUFDMUI7SUFFQUMsb0JBQW9CO1FBQ2xCLElBQUksQ0FBQ0MsS0FBSyxDQUFDQyxPQUFPO0lBQ3BCO0lBRUFDLFNBQVM7UUFDUCxJQUFJLElBQUksQ0FBQ0MsS0FBSyxDQUFDTCxRQUFRLEVBQUU7WUFDdkIsT0FBTyxNQUFNLGdEQUFnRDtRQUMvRDtRQUNBLE9BQU8sSUFBSSxDQUFDRSxLQUFLLENBQUNJLFFBQVE7SUFDNUI7SUFsQkFDLFlBQVlMLEtBQW1ELENBQUU7UUFDL0QsS0FBSyxDQUFDQTtRQUNOLElBQUksQ0FBQ0csS0FBSyxHQUFHO1lBQUVMLFVBQVU7UUFBTTtJQUNqQztBQWdCRjtBQUVBLHNFQUFzRTtBQUN0RSxNQUFNUSxnQkFBZ0JoQix3REFBT0EsQ0FDM0IsSUFBTSx3T0FBd0IsQ0FBQ2lCLEtBQUssQ0FBQztRQUNuQywyQ0FBMkM7UUFDM0MsT0FBTztZQUFFQyxTQUFTO29CQUFDLEVBQUVKLFFBQVEsRUFBd0I7cUNBQUssOERBQUNLO29CQUFJQyxXQUFVOzhCQUF1Qk47Ozs7Ozs7UUFBZ0I7SUFDbEg7Ozs7OztJQUVFTyxLQUFLO0lBQ0xDLFNBQVMsa0JBQ1AsOERBQUNDO1lBQUlILFdBQVU7OzhCQUNiLDhEQUFDRztvQkFBSUgsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDRztvQkFBSUgsV0FBVTs7Ozs7OzhCQUNmLDhEQUFDRztvQkFBSUgsV0FBVTs7Ozs7Ozs7Ozs7OztLQVhqQko7QUFpQk4sc0RBQXNEO0FBQ3RELE1BQU1RLG9CQUFvQnhCLHdEQUFPQSxDQUMvQixJQUFNLHdSQUFrQyxDQUFDeUIsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJQyxLQUFLOzs7Ozs7SUFFNUROLEtBQUs7SUFDTEMsU0FBUyxrQkFDUCw4REFBQ0M7WUFBSUgsV0FBVTtzQkFDYiw0RUFBQ0Q7MEJBQUk7Ozs7Ozs7Ozs7OztNQU5QSztBQVlOLE1BQU1JLHlCQUF5QjVCLHdEQUFPQSxDQUNwQyxJQUFNLHdVQUF3RCxDQUFDeUIsSUFBSSxDQUFDQyxDQUFBQSxNQUFPQSxJQUFJRyxPQUFPOzs7Ozs7SUFDcEZSLEtBQUs7O0FBR1QsaURBQWlEO0FBQ2pELE1BQU1TLDRCQUE0QjtRQUFDLEVBQUVoQixRQUFRLEVBQUVpQixRQUFRLEVBQTJDOztJQUNoRyxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBRy9CLCtDQUFRQSxDQUFNO0lBRXhDQyxnREFBU0E7K0NBQUM7WUFDUiw2QkFBNkI7WUFDN0Isd1VBQXdELENBQUNzQixJQUFJO3VEQUFDQyxDQUFBQTtvQkFDNURPLFNBQVNQLElBQUlHLE9BQU87Z0JBQ3RCO3NEQUFHWixLQUFLO3VEQUFDO29CQUNQLDBDQUEwQztvQkFDMUNnQixTQUFTO2dCQUNYOztRQUNGOzhDQUFHLEVBQUU7SUFFTCxJQUFJRCxPQUFPO1FBQ1QscUJBQ0UsOERBQUNSO1lBQ0NRLE9BQU9BO1lBQ1BELFVBQVVBLFlBQVk7WUFDdEJHLFFBQU87WUFDUGQsV0FBVTtzQkFFVE47Ozs7OztJQUdQO0lBRUEsd0RBQXdEO0lBQ3hELHFCQUNFLDhEQUFDUztRQUFJSCxXQUFVO2tCQUNiLDRFQUFDRDtzQkFBS0w7Ozs7Ozs7Ozs7O0FBR1o7R0FoQ01nQjtNQUFBQTtBQXVDUyxTQUFTSyxpQkFBaUIsS0FBa0Q7UUFBbEQsRUFBRUMsT0FBTyxFQUFFaEIsWUFBWSxFQUFFLEVBQXlCLEdBQWxEOztJQUN2QyxNQUFNLENBQUNaLFVBQVU2QixZQUFZLEdBQUduQywrQ0FBUUEsQ0FBQztJQUV6Q0MsZ0RBQVNBO3NDQUFDO1lBQ1JrQyxZQUFZO1FBQ2Q7cUNBQUc7UUFBQ0Q7S0FBUTtJQUVaLElBQUk1QixVQUFVO1FBQ1osc0RBQXNEO1FBQ3RELHFCQUNFLDhEQUFDZTtZQUFJSCxXQUFXLG9CQUE4QixPQUFWQTtzQkFDbEMsNEVBQUNEO2dCQUFJQyxXQUFVOzBCQUNaZ0I7Ozs7Ozs7Ozs7O0lBSVQ7SUFFQSxxQkFDRSw4REFBQ2I7UUFBSUgsV0FBVyxvQkFBOEIsT0FBVkE7a0JBQ2xDLDRFQUFDbkIsMkNBQVFBO1lBQUNxQyx3QkFDUiw4REFBQ2Y7Z0JBQUlILFdBQVU7O2tDQUNiLDhEQUFDRzt3QkFBSUgsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRzt3QkFBSUgsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRzt3QkFBSUgsV0FBVTs7Ozs7Ozs7Ozs7O3NCQUdqQiw0RUFBQ2Q7Z0JBQWNLLFNBQVMsSUFBTTBCLFlBQVk7MEJBQ3hDLDRFQUFDckI7b0JBQ0N1QixlQUFlLEVBQUU7b0JBQ25CQyxZQUFZO3dCQUNkLFVBQVU7d0JBQ1ZDLElBQUk7Z0NBQUMsRUFBRTNCLFFBQVEsRUFBRTtpREFDZiw4REFBQzJCO2dDQUFHckIsV0FBVTswQ0FDWE47Ozs7Ozs7d0JBR0w0QixJQUFJO2dDQUFDLEVBQUU1QixRQUFRLEVBQUU7aURBQ2YsOERBQUM0QjtnQ0FBR3RCLFdBQVU7MENBQ1hOOzs7Ozs7O3dCQUdMNkIsSUFBSTtnQ0FBQyxFQUFFN0IsUUFBUSxFQUFFO2lEQUNmLDhEQUFDNkI7Z0NBQUd2QixXQUFVOzBDQUNYTjs7Ozs7Ozt3QkFHTDhCLElBQUk7Z0NBQUMsRUFBRTlCLFFBQVEsRUFBRTtpREFDZiw4REFBQzhCO2dDQUFHeEIsV0FBVTswQ0FDWE47Ozs7Ozs7d0JBSUwsYUFBYTt3QkFDYitCLEdBQUc7Z0NBQUMsRUFBRS9CLFFBQVEsRUFBRTtpREFDZCw4REFBQytCO2dDQUFFekIsV0FBVTswQ0FDVk47Ozs7Ozs7d0JBSUwsa0JBQWtCO3dCQUNsQmdDLFFBQVE7Z0NBQUMsRUFBRWhDLFFBQVEsRUFBRTtpREFDbkIsOERBQUNnQztnQ0FBTzFCLFdBQVU7MENBQ2ZOOzs7Ozs7O3dCQUdMaUMsSUFBSTtnQ0FBQyxFQUFFakMsUUFBUSxFQUFFO2lEQUNmLDhEQUFDaUM7Z0NBQUczQixXQUFVOzBDQUNYTjs7Ozs7Ozt3QkFJTCxRQUFRO3dCQUNSa0MsSUFBSTtnQ0FBQyxFQUFFbEMsUUFBUSxFQUFFO2lEQUNmLDhEQUFDa0M7Z0NBQUc1QixXQUFVOzBDQUNYTjs7Ozs7Ozt3QkFHTG1DLElBQUk7Z0NBQUMsRUFBRW5DLFFBQVEsRUFBRTtpREFDZiw4REFBQ21DO2dDQUFHN0IsV0FBVTswQ0FDWE47Ozs7Ozs7d0JBR0xvQyxJQUFJO2dDQUFDLEVBQUVwQyxRQUFRLEVBQUU7aURBQ2YsOERBQUNvQztnQ0FBRzlCLFdBQVU7MENBQ1hOOzs7Ozs7O3dCQUlMLDhCQUE4Qjt3QkFDOUJxQyxNQUFNO2dDQUFDLEVBQUVDLElBQUksRUFBRUMsTUFBTSxFQUFFakMsU0FBUyxFQUFFTixRQUFRLEVBQUUsR0FBR0osT0FBWTs0QkFDekQsTUFBTTRDLFFBQVEsaUJBQWlCQyxJQUFJLENBQUNuQyxhQUFhOzRCQUNqRCxNQUFNVyxXQUFXdUIsUUFBUUEsS0FBSyxDQUFDLEVBQUUsR0FBRzs0QkFDcEMsTUFBTUUsY0FBY0MsT0FBTzNDLFVBQVU0QyxPQUFPLENBQUMsT0FBTzs0QkFFcEQsSUFBSSxDQUFDTCxRQUFRO2dDQUNYLGdFQUFnRTtnQ0FDaEUsSUFBSXRCLFVBQVU7b0NBQ1osNkNBQTZDO29DQUM3QyxxQkFDRSw4REFBQ1I7d0NBQUlILFdBQVU7OzBEQUViLDhEQUFDRztnREFBSUgsV0FBVTswREFDYiw0RUFBQ2YsbURBQVVBO29EQUNUc0QsTUFBTUg7b0RBQ05JLFNBQVE7b0RBQ1JDLE1BQUs7b0RBQ0xDLE9BQU07b0RBQ04xQyxXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQzJDO2dEQUF3QmhDLFVBQVVBOzBEQUNoQ3lCOzs7Ozs7Ozs7Ozs7Z0NBSVQsT0FBTztvQ0FDTCxzREFBc0Q7b0NBQ3RELHFCQUNFLDhEQUFDakM7d0NBQUlILFdBQVU7OzBEQUViLDhEQUFDRztnREFBSUgsV0FBVTswREFDYiw0RUFBQ2YsbURBQVVBO29EQUNUc0QsTUFBTUg7b0RBQ05JLFNBQVE7b0RBQ1JDLE1BQUs7b0RBQ0xDLE9BQU07b0RBQ04xQyxXQUFVOzs7Ozs7Ozs7OzswREFHZCw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUMrQjs4REFBTUs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dDQUlmOzRCQUNGOzRCQUVBLHFCQUNFLDhEQUFDTDtnQ0FDQy9CLFdBQVU7Z0NBQ1QsR0FBR1YsS0FBSzswQ0FFUkk7Ozs7Ozt3QkFHUDt3QkFFQSxjQUFjO3dCQUNka0QsWUFBWTtnQ0FBQyxFQUFFbEQsUUFBUSxFQUFFO2lEQUN2Qiw4REFBQ2tEO2dDQUFXNUMsV0FBVTswQ0FDbkJOOzs7Ozs7O3dCQUlMLFFBQVE7d0JBQ1JtRCxHQUFHO2dDQUFDLEVBQUVuRCxRQUFRLEVBQUVvRCxJQUFJLEVBQUU7aURBQ3BCLDhEQUFDRDtnQ0FDQ0MsTUFBTUE7Z0NBQ05DLFFBQU87Z0NBQ1BDLEtBQUk7Z0NBQ0poRCxXQUFVOzBDQUVUTjs7Ozs7Ozt3QkFJTCxTQUFTO3dCQUNUdUQsT0FBTztnQ0FBQyxFQUFFdkQsUUFBUSxFQUFFO2lEQUNsQiw4REFBQ1M7Z0NBQUlILFdBQVU7MENBQ2IsNEVBQUNpRDtvQ0FBTWpELFdBQVU7OENBQ2ROOzs7Ozs7Ozs7Ozs7d0JBSVB3RCxPQUFPO2dDQUFDLEVBQUV4RCxRQUFRLEVBQUU7aURBQ2xCLDhEQUFDd0Q7Z0NBQU1sRCxXQUFVOzBDQUNkTjs7Ozs7Ozt3QkFHTHlELE9BQU87Z0NBQUMsRUFBRXpELFFBQVEsRUFBRTtpREFDbEIsOERBQUN5RDtnQ0FBTW5ELFdBQVU7MENBQ2ROOzs7Ozs7O3dCQUdMMEQsSUFBSTtnQ0FBQyxFQUFFMUQsUUFBUSxFQUFFO2lEQUNmLDhEQUFDMEQ7Z0NBQUdwRCxXQUFVOzBDQUNYTjs7Ozs7Ozt3QkFHTDJELElBQUk7Z0NBQUMsRUFBRTNELFFBQVEsRUFBRTtpREFDZiw4REFBQzJEO2dDQUFHckQsV0FBVTswQ0FDWE47Ozs7Ozs7d0JBR0w0RCxJQUFJO2dDQUFDLEVBQUU1RCxRQUFRLEVBQUU7aURBQ2YsOERBQUM0RDtnQ0FBR3RELFdBQVU7MENBQ1hOOzs7Ozs7O3dCQUlMLGtCQUFrQjt3QkFDbEI2RCxJQUFJLGtCQUNGLDhEQUFDQTtnQ0FBR3ZELFdBQVU7Ozs7OztvQkFFaEI7OEJBRUdnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTVg7SUFyTndCRDtNQUFBQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXE1hcmtkb3duUmVuZGVyZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IGR5bmFtaWMgZnJvbSAnbmV4dC9keW5hbWljJztcbmltcG9ydCB7IFN1c3BlbnNlLCB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBDb21wb25lbnQsIFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCBDb3B5QnV0dG9uIGZyb20gJy4vQ29weUJ1dHRvbic7XG5cbi8vIEVycm9yIEJvdW5kYXJ5IGNvbXBvbmVudCBmb3IgaGFuZGxpbmcgbWFya2Rvd24gcmVuZGVyaW5nIGVycm9yc1xuY2xhc3MgRXJyb3JCb3VuZGFyeSBleHRlbmRzIENvbXBvbmVudDxcbiAgeyBjaGlsZHJlbjogUmVhY3ROb2RlOyBvbkVycm9yOiAoKSA9PiB2b2lkIH0sXG4gIHsgaGFzRXJyb3I6IGJvb2xlYW4gfVxuPiB7XG4gIGNvbnN0cnVjdG9yKHByb3BzOiB7IGNoaWxkcmVuOiBSZWFjdE5vZGU7IG9uRXJyb3I6ICgpID0+IHZvaWQgfSkge1xuICAgIHN1cGVyKHByb3BzKTtcbiAgICB0aGlzLnN0YXRlID0geyBoYXNFcnJvcjogZmFsc2UgfTtcbiAgfVxuXG4gIHN0YXRpYyBnZXREZXJpdmVkU3RhdGVGcm9tRXJyb3IoKSB7XG4gICAgcmV0dXJuIHsgaGFzRXJyb3I6IHRydWUgfTtcbiAgfVxuXG4gIGNvbXBvbmVudERpZENhdGNoKCkge1xuICAgIHRoaXMucHJvcHMub25FcnJvcigpO1xuICB9XG5cbiAgcmVuZGVyKCkge1xuICAgIGlmICh0aGlzLnN0YXRlLmhhc0Vycm9yKSB7XG4gICAgICByZXR1cm4gbnVsbDsgLy8gTGV0IHBhcmVudCBjb21wb25lbnQgaGFuZGxlIHRoZSBlcnJvciBkaXNwbGF5XG4gICAgfVxuICAgIHJldHVybiB0aGlzLnByb3BzLmNoaWxkcmVuO1xuICB9XG59XG5cbi8vIER5bmFtaWMgaW1wb3J0cyB0byBpc29sYXRlIHByb2JsZW1hdGljIHBhY2thZ2VzIHdpdGggZXJyb3IgaGFuZGxpbmdcbmNvbnN0IFJlYWN0TWFya2Rvd24gPSBkeW5hbWljKFxuICAoKSA9PiBpbXBvcnQoJ3JlYWN0LW1hcmtkb3duJykuY2F0Y2goKCkgPT4ge1xuICAgIC8vIEZhbGxiYWNrIGlmIHJlYWN0LW1hcmtkb3duIGZhaWxzIHRvIGxvYWRcbiAgICByZXR1cm4geyBkZWZhdWx0OiAoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBzdHJpbmcgfSkgPT4gPHByZSBjbGFzc05hbWU9XCJ3aGl0ZXNwYWNlLXByZS13cmFwXCI+e2NoaWxkcmVufTwvcHJlPiB9O1xuICB9KSxcbiAge1xuICAgIHNzcjogZmFsc2UsXG4gICAgbG9hZGluZzogKCkgPT4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXB1bHNlIGJnLWdyYXktMTAwIHJvdW5kZWQgcC00XCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLWdyYXktMzAwIHJvdW5kZWQgbWItMlwiPjwvZGl2PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTMwMCByb3VuZGVkIG1iLTIgdy0zLzRcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctZ3JheS0zMDAgcm91bmRlZCB3LTEvMlwiPjwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG4pO1xuXG4vLyBEeW5hbWljIGltcG9ydCBmb3Igc3ludGF4IGhpZ2hsaWdodGVyIHdpdGggZmFsbGJhY2tcbmNvbnN0IFN5bnRheEhpZ2hsaWdodGVyID0gZHluYW1pYyhcbiAgKCkgPT4gaW1wb3J0KCdyZWFjdC1zeW50YXgtaGlnaGxpZ2h0ZXInKS50aGVuKG1vZCA9PiBtb2QuUHJpc20pLFxuICB7XG4gICAgc3NyOiBmYWxzZSxcbiAgICBsb2FkaW5nOiAoKSA9PiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyYXktOTAwIHRleHQtZ3JheS0xMDAgcC00IHJvdW5kZWQtbGcgdGV4dC1zbSBmb250LW1vbm8gb3ZlcmZsb3cteC1hdXRvXCI+XG4gICAgICAgIDxwcmU+TG9hZGluZyBzeW50YXggaGlnaGxpZ2h0ZXIuLi48L3ByZT5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuKTtcblxuY29uc3Qgc3ludGF4SGlnaGxpZ2h0ZXJTdHlsZSA9IGR5bmFtaWMoXG4gICgpID0+IGltcG9ydCgncmVhY3Qtc3ludGF4LWhpZ2hsaWdodGVyL2Rpc3QvZXNtL3N0eWxlcy9wcmlzbScpLnRoZW4obW9kID0+IG1vZC5vbmVEYXJrKSxcbiAgeyBzc3I6IGZhbHNlIH1cbik7XG5cbi8vIEVuaGFuY2VkIHN5bnRheCBoaWdobGlnaHRlciB3aXRoIHByb3BlciBjb2xvcnNcbmNvbnN0IEVuaGFuY2VkU3ludGF4SGlnaGxpZ2h0ZXIgPSAoeyBjaGlsZHJlbiwgbGFuZ3VhZ2UgfTogeyBjaGlsZHJlbjogc3RyaW5nOyBsYW5ndWFnZT86IHN0cmluZyB9KSA9PiB7XG4gIGNvbnN0IFtzdHlsZSwgc2V0U3R5bGVdID0gdXNlU3RhdGU8YW55PihudWxsKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIExvYWQgdGhlIHN0eWxlIGR5bmFtaWNhbGx5XG4gICAgaW1wb3J0KCdyZWFjdC1zeW50YXgtaGlnaGxpZ2h0ZXIvZGlzdC9lc20vc3R5bGVzL3ByaXNtJykudGhlbihtb2QgPT4ge1xuICAgICAgc2V0U3R5bGUobW9kLm9uZURhcmspO1xuICAgIH0pLmNhdGNoKCgpID0+IHtcbiAgICAgIC8vIEZhbGxiYWNrIHRvIG51bGwgaWYgc3R5bGUgbG9hZGluZyBmYWlsc1xuICAgICAgc2V0U3R5bGUobnVsbCk7XG4gICAgfSk7XG4gIH0sIFtdKTtcblxuICBpZiAoc3R5bGUpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPFN5bnRheEhpZ2hsaWdodGVyXG4gICAgICAgIHN0eWxlPXtzdHlsZX1cbiAgICAgICAgbGFuZ3VhZ2U9e2xhbmd1YWdlIHx8ICd0ZXh0J31cbiAgICAgICAgUHJlVGFnPVwiZGl2XCJcbiAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbVwiXG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvU3ludGF4SGlnaGxpZ2h0ZXI+XG4gICAgKTtcbiAgfVxuXG4gIC8vIEZhbGxiYWNrIHdoaWxlIGxvYWRpbmcgb3IgaWYgc3ludGF4IGhpZ2hsaWdodGVyIGZhaWxzXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJiZy1ncmF5LTkwMCB0ZXh0LWdyYXktMTAwIHAtNCByb3VuZGVkLWxnIHRleHQtc20gZm9udC1tb25vIG92ZXJmbG93LXgtYXV0b1wiPlxuICAgICAgPHByZT57Y2hpbGRyZW59PC9wcmU+XG4gICAgPC9kaXY+XG4gICk7XG59O1xuXG5pbnRlcmZhY2UgTWFya2Rvd25SZW5kZXJlclByb3BzIHtcbiAgY29udGVudDogc3RyaW5nO1xuICBjbGFzc05hbWU/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE1hcmtkb3duUmVuZGVyZXIoeyBjb250ZW50LCBjbGFzc05hbWUgPSAnJyB9OiBNYXJrZG93blJlbmRlcmVyUHJvcHMpIHtcbiAgY29uc3QgW2hhc0Vycm9yLCBzZXRIYXNFcnJvcl0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRIYXNFcnJvcihmYWxzZSk7XG4gIH0sIFtjb250ZW50XSk7XG5cbiAgaWYgKGhhc0Vycm9yKSB7XG4gICAgLy8gRmFsbGJhY2sgdG8gc2ltcGxlIHRleHQgcmVuZGVyaW5nIGlmIG1hcmtkb3duIGZhaWxzXG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgbWFya2Rvd24tY29udGVudCAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAgPHByZSBjbGFzc05hbWU9XCJ3aGl0ZXNwYWNlLXByZS13cmFwIHRleHQtc20gdGV4dC1ncmF5LTkwMCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICB7Y29udGVudH1cbiAgICAgICAgPC9wcmU+XG4gICAgICA8L2Rpdj5cbiAgICApO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT17YG1hcmtkb3duLWNvbnRlbnQgJHtjbGFzc05hbWV9YH0+XG4gICAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtcHVsc2UgYmctZ3JheS0xMDAgcm91bmRlZCBwLTRcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTMwMCByb3VuZGVkIG1iLTJcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTMwMCByb3VuZGVkIG1iLTIgdy0zLzRcIj48L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtNCBiZy1ncmF5LTMwMCByb3VuZGVkIHctMS8yXCI+PC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgfT5cbiAgICAgICAgPEVycm9yQm91bmRhcnkgb25FcnJvcj17KCkgPT4gc2V0SGFzRXJyb3IodHJ1ZSl9PlxuICAgICAgICAgIDxSZWFjdE1hcmtkb3duXG4gICAgICAgICAgICByZW1hcmtQbHVnaW5zPXtbXX1cbiAgICAgICAgICBjb21wb25lbnRzPXt7XG4gICAgICAgIC8vIEhlYWRlcnNcbiAgICAgICAgaDE6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LWJvbGQgbWItMyBtdC00IGZpcnN0Om10LTAgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvaDE+XG4gICAgICAgICksXG4gICAgICAgIGgyOiAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1ib2xkIG1iLTIgbXQtMyBmaXJzdDptdC0wIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L2gyPlxuICAgICAgICApLFxuICAgICAgICBoMzogKHsgY2hpbGRyZW4gfSkgPT4gKFxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgZm9udC1ib2xkIG1iLTIgbXQtMyBmaXJzdDptdC0wIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L2gzPlxuICAgICAgICApLFxuICAgICAgICBoNDogKHsgY2hpbGRyZW4gfSkgPT4gKFxuICAgICAgICAgIDxoNCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtYm9sZCBtYi0xIG10LTIgZmlyc3Q6bXQtMCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9oND5cbiAgICAgICAgKSxcblxuICAgICAgICAvLyBQYXJhZ3JhcGhzXG4gICAgICAgIHA6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtYi0zIGxhc3Q6bWItMCBsZWFkaW5nLXJlbGF4ZWQgdGV4dC1ncmF5LTkwMCBicmVhay13b3Jkc1wiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgKSxcbiAgICAgICAgXG4gICAgICAgIC8vIEJvbGQgYW5kIGl0YWxpY1xuICAgICAgICBzdHJvbmc6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8c3Ryb25nIGNsYXNzTmFtZT1cImZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9zdHJvbmc+XG4gICAgICAgICksXG4gICAgICAgIGVtOiAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gICAgICAgICAgPGVtIGNsYXNzTmFtZT1cIml0YWxpYyB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9lbT5cbiAgICAgICAgKSxcblxuICAgICAgICAvLyBMaXN0c1xuICAgICAgICB1bDogKHsgY2hpbGRyZW4gfSkgPT4gKFxuICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJsaXN0LWRpc2MgbGlzdC1pbnNpZGUgbWItMyBzcGFjZS15LTEgdGV4dC1ncmF5LTkwMFwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvdWw+XG4gICAgICAgICksXG4gICAgICAgIG9sOiAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gICAgICAgICAgPG9sIGNsYXNzTmFtZT1cImxpc3QtZGVjaW1hbCBsaXN0LWluc2lkZSBtYi0zIHNwYWNlLXktMSB0ZXh0LWdyYXktOTAwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9vbD5cbiAgICAgICAgKSxcbiAgICAgICAgbGk6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8bGkgY2xhc3NOYW1lPVwibGVhZGluZy1yZWxheGVkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L2xpPlxuICAgICAgICApLFxuICAgICAgICBcbiAgICAgICAgLy8gQ29kZSBibG9ja3MgYW5kIGlubGluZSBjb2RlXG4gICAgICAgIGNvZGU6ICh7IG5vZGUsIGlubGluZSwgY2xhc3NOYW1lLCBjaGlsZHJlbiwgLi4ucHJvcHMgfTogYW55KSA9PiB7XG4gICAgICAgICAgY29uc3QgbWF0Y2ggPSAvbGFuZ3VhZ2UtKFxcdyspLy5leGVjKGNsYXNzTmFtZSB8fCAnJyk7XG4gICAgICAgICAgY29uc3QgbGFuZ3VhZ2UgPSBtYXRjaCA/IG1hdGNoWzFdIDogJyc7XG4gICAgICAgICAgY29uc3QgY29kZUNvbnRlbnQgPSBTdHJpbmcoY2hpbGRyZW4pLnJlcGxhY2UoL1xcbiQvLCAnJyk7XG5cbiAgICAgICAgICBpZiAoIWlubGluZSkge1xuICAgICAgICAgICAgLy8gSGFuZGxlIGNvZGUgYmxvY2tzIChib3RoIHdpdGggYW5kIHdpdGhvdXQgbGFuZ3VhZ2UgZGV0ZWN0aW9uKVxuICAgICAgICAgICAgaWYgKGxhbmd1YWdlKSB7XG4gICAgICAgICAgICAgIC8vIENvZGUgYmxvY2sgd2l0aCBzaW1wbGUgc3ludGF4IGhpZ2hsaWdodGluZ1xuICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXktMyByb3VuZGVkLWxnIG92ZXJmbG93LWhpZGRlbiByZWxhdGl2ZSBncm91cFwiPlxuICAgICAgICAgICAgICAgICAgey8qIENvcHkgYnV0dG9uIGZvciBjb2RlIGJsb2NrcyAtIHBvc2l0aW9uZWQgb24gdG9wIHJpZ2h0IHdpdGggYmV0dGVyIHZpc2liaWxpdHkgKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0zIHJpZ2h0LTMgei0yMCBvcGFjaXR5LTcwIGhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0yMDBcIj5cbiAgICAgICAgICAgICAgICAgICAgPENvcHlCdXR0b25cbiAgICAgICAgICAgICAgICAgICAgICB0ZXh0PXtjb2RlQ29udGVudH1cbiAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiY29kZVwiXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cInNtXCJcbiAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkNvcHkgY29kZVwiXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS04MDAvODAgaG92ZXI6YmctZ3JheS03MDAvOTAgYmFja2Ryb3AtYmx1ci1zbSBib3JkZXIgYm9yZGVyLWdyYXktNjAwLzUwXCJcbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPFNpbXBsZVN5bnRheEhpZ2hsaWdodGVyIGxhbmd1YWdlPXtsYW5ndWFnZX0+XG4gICAgICAgICAgICAgICAgICAgIHtjb2RlQ29udGVudH1cbiAgICAgICAgICAgICAgICAgIDwvU2ltcGxlU3ludGF4SGlnaGxpZ2h0ZXI+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAvLyBDb2RlIGJsb2NrIHdpdGhvdXQgbGFuZ3VhZ2UgKHBsYWluIHRleHQgY29kZSBibG9jaylcbiAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm15LTMgcm91bmRlZC1sZyBvdmVyZmxvdy1oaWRkZW4gcmVsYXRpdmUgZ3JvdXAgYmctZ3JheS05MDAgdGV4dC1ncmF5LTEwMFwiPlxuICAgICAgICAgICAgICAgICAgey8qIENvcHkgYnV0dG9uIGZvciBwbGFpbiBjb2RlIGJsb2NrcyAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTMgcmlnaHQtMyB6LTIwIG9wYWNpdHktNzAgaG92ZXI6b3BhY2l0eS0xMDAgdHJhbnNpdGlvbi1vcGFjaXR5IGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgICAgICAgICAgICA8Q29weUJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgIHRleHQ9e2NvZGVDb250ZW50fVxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJjb2RlXCJcbiAgICAgICAgICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICAgICAgICAgIHRpdGxlPVwiQ29weSBjb2RlXCJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ncmF5LTgwMC84MCBob3ZlcjpiZy1ncmF5LTcwMC85MCBiYWNrZHJvcC1ibHVyLXNtIGJvcmRlciBib3JkZXItZ3JheS02MDAvNTBcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cInAtNCB0ZXh0LXNtIGZvbnQtbW9ubyBvdmVyZmxvdy14LWF1dG9cIj5cbiAgICAgICAgICAgICAgICAgICAgPGNvZGU+e2NvZGVDb250ZW50fTwvY29kZT5cbiAgICAgICAgICAgICAgICAgIDwvcHJlPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICBcbiAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgPGNvZGVcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctZ3JheS0xMDAgdGV4dC1ncmF5LTkwMCBweC0xLjUgcHktMC41IHJvdW5kZWQgdGV4dC1zbSBmb250LW1vbm9cIlxuICAgICAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICAgIDwvY29kZT5cbiAgICAgICAgICApO1xuICAgICAgICB9LFxuXG4gICAgICAgIC8vIEJsb2NrcXVvdGVzXG4gICAgICAgIGJsb2NrcXVvdGU6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8YmxvY2txdW90ZSBjbGFzc05hbWU9XCJib3JkZXItbC00IGJvcmRlci1vcmFuZ2UtNTAwIHBsLTQgbXktMyBpdGFsaWMgdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvYmxvY2txdW90ZT5cbiAgICAgICAgKSxcblxuICAgICAgICAvLyBMaW5rc1xuICAgICAgICBhOiAoeyBjaGlsZHJlbiwgaHJlZiB9KSA9PiAoXG4gICAgICAgICAgPGFcbiAgICAgICAgICAgIGhyZWY9e2hyZWZ9XG4gICAgICAgICAgICB0YXJnZXQ9XCJfYmxhbmtcIlxuICAgICAgICAgICAgcmVsPVwibm9vcGVuZXIgbm9yZWZlcnJlclwiXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LW9yYW5nZS02MDAgaG92ZXI6dGV4dC1vcmFuZ2UtNzAwIHVuZGVybGluZSB0cmFuc2l0aW9uLWNvbG9ycyBkdXJhdGlvbi0yMDBcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L2E+XG4gICAgICAgICksXG4gICAgICAgIFxuICAgICAgICAvLyBUYWJsZXNcbiAgICAgICAgdGFibGU6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm92ZXJmbG93LXgtYXV0byBteS0zXCI+XG4gICAgICAgICAgICA8dGFibGUgY2xhc3NOYW1lPVwibWluLXctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMjAwIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPC90YWJsZT5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgKSxcbiAgICAgICAgdGhlYWQ6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8dGhlYWQgY2xhc3NOYW1lPVwiYmctZ3JheS01MFwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvdGhlYWQ+XG4gICAgICAgICksXG4gICAgICAgIHRib2R5OiAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gICAgICAgICAgPHRib2R5IGNsYXNzTmFtZT1cImRpdmlkZS15IGRpdmlkZS1ncmF5LTIwMCBiZy13aGl0ZVwiPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgIDwvdGJvZHk+XG4gICAgICAgICksXG4gICAgICAgIHRyOiAoeyBjaGlsZHJlbiB9KSA9PiAoXG4gICAgICAgICAgPHRyIGNsYXNzTmFtZT1cImhvdmVyOmJnLWdyYXktNTBcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L3RyPlxuICAgICAgICApLFxuICAgICAgICB0aDogKHsgY2hpbGRyZW4gfSkgPT4gKFxuICAgICAgICAgIDx0aCBjbGFzc05hbWU9XCJweC0zIHB5LTIgdGV4dC1sZWZ0IHRleHQteHMgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCB1cHBlcmNhc2UgdHJhY2tpbmctd2lkZXIgYm9yZGVyLWIgYm9yZGVyLWdyYXktMjAwXCI+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC90aD5cbiAgICAgICAgKSxcbiAgICAgICAgdGQ6ICh7IGNoaWxkcmVuIH0pID0+IChcbiAgICAgICAgICA8dGQgY2xhc3NOYW1lPVwicHgtMyBweS0yIHRleHQtc20gdGV4dC1ncmF5LTkwMCBib3JkZXItYiBib3JkZXItZ3JheS0yMDBcIj5cbiAgICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgICA8L3RkPlxuICAgICAgICApLFxuICAgICAgICBcbiAgICAgICAgLy8gSG9yaXpvbnRhbCBydWxlXG4gICAgICAgIGhyOiAoKSA9PiAoXG4gICAgICAgICAgPGhyIGNsYXNzTmFtZT1cIm15LTQgYm9yZGVyLWdyYXktMjAwXCIgLz5cbiAgICAgICAgKSxcbiAgICAgICAgfX1cbiAgICAgICAgPlxuICAgICAgICAgIHtjb250ZW50fVxuICAgICAgICA8L1JlYWN0TWFya2Rvd24+XG4gICAgICAgIDwvRXJyb3JCb3VuZGFyeT5cbiAgICAgIDwvU3VzcGVuc2U+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiZHluYW1pYyIsIlN1c3BlbnNlIiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJDb21wb25lbnQiLCJDb3B5QnV0dG9uIiwiRXJyb3JCb3VuZGFyeSIsImdldERlcml2ZWRTdGF0ZUZyb21FcnJvciIsImhhc0Vycm9yIiwiY29tcG9uZW50RGlkQ2F0Y2giLCJwcm9wcyIsIm9uRXJyb3IiLCJyZW5kZXIiLCJzdGF0ZSIsImNoaWxkcmVuIiwiY29uc3RydWN0b3IiLCJSZWFjdE1hcmtkb3duIiwiY2F0Y2giLCJkZWZhdWx0IiwicHJlIiwiY2xhc3NOYW1lIiwic3NyIiwibG9hZGluZyIsImRpdiIsIlN5bnRheEhpZ2hsaWdodGVyIiwidGhlbiIsIm1vZCIsIlByaXNtIiwic3ludGF4SGlnaGxpZ2h0ZXJTdHlsZSIsIm9uZURhcmsiLCJFbmhhbmNlZFN5bnRheEhpZ2hsaWdodGVyIiwibGFuZ3VhZ2UiLCJzdHlsZSIsInNldFN0eWxlIiwiUHJlVGFnIiwiTWFya2Rvd25SZW5kZXJlciIsImNvbnRlbnQiLCJzZXRIYXNFcnJvciIsImZhbGxiYWNrIiwicmVtYXJrUGx1Z2lucyIsImNvbXBvbmVudHMiLCJoMSIsImgyIiwiaDMiLCJoNCIsInAiLCJzdHJvbmciLCJlbSIsInVsIiwib2wiLCJsaSIsImNvZGUiLCJub2RlIiwiaW5saW5lIiwibWF0Y2giLCJleGVjIiwiY29kZUNvbnRlbnQiLCJTdHJpbmciLCJyZXBsYWNlIiwidGV4dCIsInZhcmlhbnQiLCJzaXplIiwidGl0bGUiLCJTaW1wbGVTeW50YXhIaWdobGlnaHRlciIsImJsb2NrcXVvdGUiLCJhIiwiaHJlZiIsInRhcmdldCIsInJlbCIsInRhYmxlIiwidGhlYWQiLCJ0Ym9keSIsInRyIiwidGgiLCJ0ZCIsImhyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/MarkdownRenderer.tsx\n"));

/***/ })

});